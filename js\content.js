// 内容脚本，用于与页面交互
// 这个脚本会被注入到所有匹配的页面中

// 创建并添加悬浮模块
function createFloatingModule() {
  // 检查是否是1688网站或localhost:952
  const is1688Site = window.location.href.includes('1688.com');
  const isPddSite = window.location.href.includes('pinduoduo.com') || window.location.href.includes('yangkeduo.com');
  const isLocalhost952 = window.location.href.includes('localhost:952');
  
  // 创建悬浮模块容器
  const floatingModule = document.createElement('div');
  floatingModule.id = 'mall-token-floating-module';
  floatingModule.style.cssText = `
    position: fixed;
    right: 20px;
    top: 100px;
    width: 250px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    transition: all 0.3s ease;
    overflow: hidden;
  `;
  
  // 创建标题栏
  const titleBar = document.createElement('div');
  titleBar.style.cssText = `
    background-color: #1890ff;
    color: white;
    padding: 8px 15px;
    font-weight: bold;
    cursor: move;
    display: flex;
    justify-content: space-between;
    align-items: center;
  `;
  titleBar.textContent = 'Mall Token Extension';
  
  // 添加最小化/最大化按钮
  const toggleButton = document.createElement('span');
  toggleButton.textContent = '−';
  toggleButton.style.cssText = `
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
  `;
  toggleButton.title = '最小化';
  
  // 添加内容区域
  const contentArea = document.createElement('div');
  contentArea.style.cssText = `
    padding: 15px;
  `;
  
  // 根据网站类型显示不同内容
  if (is1688Site) {
    // 在1688网站上显示采集和购买功能
    contentArea.innerHTML = `
      <div style="margin-bottom: 15px;">
        <button id="collect-product-btn" style="
          background-color: #1890ff;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          width: 100%;
          margin-bottom: 10px;
        ">采集商品</button>
        
        <button id="buy-product-btn" style="
          background-color: #52c41a;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          width: 100%;
        ">购买商品</button>
      </div>
      
      <div id="floating-status" style="
        font-size: 13px;
        color: #666;
        margin-top: 10px;
      ">准备就绪</div>
    `;
  } else if (isPddSite) {
    // 在拼多多网站上显示采集功能
    contentArea.innerHTML = `
      <div style="margin-bottom: 15px;">
        <button id="collect-pdd-btn" style="
          background-color: #E02E24;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          width: 100%;
          margin-bottom: 10px;
        ">采集拼多多商品</button>
      </div>
      
      <div id="floating-status" style="
        font-size: 13px;
        color: #666;
        margin-top: 10px;
      ">准备就绪</div>
    `;
  } else if (isLocalhost952) {
    // 在localhost:952上显示获取token功能
    contentArea.innerHTML = `
      <div style="margin-bottom: 15px;">
        <button id="extract-token-btn" style="
          background-color: #1890ff;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          width: 100%;
          margin-bottom: 10px;
        ">获取token</button>
        
        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
          <button id="collect-1688-btn" style="
            background-color: #FF6A00;
            color: white;
            border: none;
            padding: 8px 10px;
            border-radius: 4px;
            cursor: pointer;
            width: 48%;
          ">1688采集</button>
          
          <button id="collect-pdd-btn" style="
            background-color: #E02E24;
            color: white;
            border: none;
            padding: 8px 10px;
            border-radius: 4px;
            cursor: pointer;
            width: 48%;
          ">拼多多采集</button>
        </div>
      </div>
      
      <div id="floating-status" style="
        font-size: 13px;
        color: #666;
        margin-top: 10px;
      ">准备获取token</div>
    `;
  } else {
    // 在其他网站上显示获取token功能
    contentArea.innerHTML = `
      <div style="margin-bottom: 15px;">
        <button id="extract-token-btn" style="
          background-color: #1890ff;
          color: white;
          border: none;
          padding: 8px 15px;
          border-radius: 4px;
          cursor: pointer;
          width: 100%;
        ">获取Token</button>
      </div>
      
      <div id="floating-status" style="
        font-size: 13px;
        color: #666;
        margin-top: 10px;
      ">准备就绪</div>
    `;
  }
  
  // 组装悬浮模块
  titleBar.appendChild(toggleButton);
  floatingModule.appendChild(titleBar);
  floatingModule.appendChild(contentArea);
  document.body.appendChild(floatingModule);
  
  // 添加拖拽功能
  let isDragging = false;
  let offsetX, offsetY;
  
  titleBar.addEventListener('mousedown', function(e) {
    isDragging = true;
    offsetX = e.clientX - floatingModule.getBoundingClientRect().left;
    offsetY = e.clientY - floatingModule.getBoundingClientRect().top;
  });
  
  document.addEventListener('mousemove', function(e) {
    if (isDragging) {
      floatingModule.style.left = (e.clientX - offsetX) + 'px';
      floatingModule.style.top = (e.clientY - offsetY) + 'px';
      floatingModule.style.right = 'auto';
    }
  });
  
  document.addEventListener('mouseup', function() {
    isDragging = false;
  });
  
  // 添加最小化/最大化功能
  let isMinimized = false;
  toggleButton.addEventListener('click', function() {
    if (isMinimized) {
      contentArea.style.display = 'block';
      toggleButton.textContent = '−';
      toggleButton.title = '最小化';
      isMinimized = false;
    } else {
      contentArea.style.display = 'none';
      toggleButton.textContent = '+';
      toggleButton.title = '最大化';
      isMinimized = true;
    }
  });
  
  // 添加按钮事件监听
  if (is1688Site) {
    // 1688网站上的按钮事件
    document.getElementById('collect-product-btn').addEventListener('click', function() {
      const statusElement = document.getElementById('floating-status');
      statusElement.textContent = '正在采集商品...';
      statusElement.style.color = '#1890ff';
      
      try {
        // 检查是否是商品详情页
        if (!window.location.href.includes('detail.1688.com') && 
            !window.location.href.match(/\/(\d+)\.html/)) {
          statusElement.textContent = '当前页面不是商品详情页';
          statusElement.style.color = '#f5222d';
          return;
        }
        
        // 提取商品信息
        const productParam = extractProductInfo();
        
        if (productParam) {
          // 保存采集到的商品信息
          chrome.storage.local.set({ collectedProduct: productParam }, function() {
            statusElement.textContent = '采集成功';
            statusElement.style.color = '#52c41a';
            
            // 显示采集到的商品名称
            setTimeout(() => {
              statusElement.textContent = `已采集: ${productParam.prodName.substring(0, 15)}${productParam.prodName.length > 15 ? '...' : ''}`;
            }, 1500);
            
            // 记录到控制台
            console.log('采集的商品信息:', productParam);
            
            // 生成SQL语句并复制到剪贴板
            const sqlStatement = generateSqlStatement(productParam);
            navigator.clipboard.writeText(sqlStatement)
              .then(() => {
                console.log('SQL语句已复制到剪贴板');
              })
              .catch(err => {
                console.error('复制SQL语句失败:', err);
              });
          });
        } else {
          statusElement.textContent = '采集失败: 无法获取商品信息';
          statusElement.style.color = '#f5222d';
        }
      } catch (error) {
        console.error('采集过程中出错:', error);
        statusElement.textContent = '采集失败: ' + error.message;
        statusElement.style.color = '#f5222d';
      }
    });
    
    document.getElementById('buy-product-btn').addEventListener('click', function() {
      const statusElement = document.getElementById('floating-status');
      
      chrome.storage.local.get(['token', 'collectedProduct'], function(result) {
        if (!result.token) {
          statusElement.textContent = '请先获取token';
          statusElement.style.color = '#f5222d';
          return;
        }
        
        if (!result.collectedProduct) {
          statusElement.textContent = '请先采集商品';
          statusElement.style.color = '#f5222d';
          return;
        }
        
        statusElement.textContent = '正在购买商品...';
        statusElement.style.color = '#1890ff';
        
        chrome.runtime.sendMessage({ 
          action: 'buyProduct',
          product: result.collectedProduct,
          token: result.token
        }, function(response) {
          if (response && response.success) {
            statusElement.textContent = '购买成功';
            statusElement.style.color = '#52c41a';
          } else {
            statusElement.textContent = '购买失败: ' + (response?.error || '未知错误');
            statusElement.style.color = '#f5222d';
          }
        });
      });
    });
  } else if (isPddSite) {
    // 拼多多网站上的按钮事件
    document.getElementById('collect-pdd-btn').addEventListener('click', function() {
      const statusElement = document.getElementById('floating-status');
      statusElement.textContent = '正在采集拼多多商品...';
      statusElement.style.color = '#1890ff';
      
      // 提取拼多多商品信息
      const pddProduct = extractPddProductInfo();
      
      if (pddProduct) {
        // 保存采集到的商品信息
        chrome.storage.local.set({ collectedPddProduct: pddProduct }, function() {
          statusElement.textContent = '拼多多商品采集成功';
          statusElement.style.color = '#52c41a';
        });
      } else {
        statusElement.textContent = '拼多多商品采集失败';
        statusElement.style.color = '#f5222d';
      }
    });
  } else if (isLocalhost952) {
    // localhost:952上的按钮事件
    document.getElementById('extract-token-btn').addEventListener('click', function() {
      const statusElement = document.getElementById('floating-status');
      statusElement.textContent = '正在获取token...';
      statusElement.style.color = '#1890ff';
      
      // 尝试提取token
      const token = extractToken();
      
      if (token) {
        // 保存token
        chrome.storage.local.set({ token }, function() {
          statusElement.textContent = 'token获取成功';
          statusElement.style.color = '#52c41a';
        });
      } else {
        statusElement.textContent = '无法获取token';
        statusElement.style.color = '#f5222d';
      }
    });
    
    // 1688采集按钮事件
    document.getElementById('collect-1688-btn').addEventListener('click', function() {
      const statusElement = document.getElementById('floating-status');
      
      chrome.storage.local.get('token', function(result) {
        if (!result.token) {
          statusElement.textContent = '请先获取token';
          statusElement.style.color = '#f5222d';
          return;
        }
        
        // 打开1688采集页面
        chrome.runtime.sendMessage({ 
          action: 'open1688CollectionPage',
          token: result.token
        }, function(response) {
          if (response && response.success) {
            statusElement.textContent = '已打开1688采集页面';
            statusElement.style.color = '#52c41a';
          } else {
            statusElement.textContent = '打开1688采集页面失败';
            statusElement.style.color = '#f5222d';
          }
        });
      });
    });
    
    // 拼多多采集按钮事件
    document.getElementById('collect-pdd-btn').addEventListener('click', function() {
      const statusElement = document.getElementById('floating-status');
      
      chrome.storage.local.get('token', function(result) {
        if (!result.token) {
          statusElement.textContent = '请先获取token';
          statusElement.style.color = '#f5222d';
          return;
        }
        
        // 打开拼多多采集页面
        chrome.runtime.sendMessage({ 
          action: 'openPddCollectionPage',
          token: result.token
        }, function(response) {
          if (response && response.success) {
            statusElement.textContent = '已打开拼多多采集页面';
            statusElement.style.color = '#52c41a';
          } else {
            statusElement.textContent = '打开拼多多采集页面失败';
            statusElement.style.color = '#f5222d';
          }
        });
      });
    });
  } else {
    // 其他网站上的按钮事件
    document.getElementById('extract-token-btn').addEventListener('click', function() {
      const statusElement = document.getElementById('floating-status');
      statusElement.textContent = '正在获取Token...';
      statusElement.style.color = '#1890ff';
      
      // 尝试提取token
      const token = extractToken();
      
      if (token) {
        // 保存token
        chrome.storage.local.set({ token }, function() {
          statusElement.textContent = 'Token获取成功';
          statusElement.style.color = '#52c41a';
        });
      } else {
        statusElement.textContent = '无法获取Token';
        statusElement.style.color = '#f5222d';
      }
    });
  }
  
  return floatingModule;
}

// 从页面中提取token的函数
function extractToken() {
  try {
    // 特殊处理localhost:952域名
    if (window.location.href.includes('localhost:952')) {
      console.log('检测到localhost:952域名，尝试从cookie中获取Authorization');
      
      // 从cookie中提取Authorization
      const cookies = document.cookie.split(';');
      let authToken = null;
      
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'Authorization') {
          authToken = value;
          console.log('成功从cookie中获取Authorization:', authToken);
          break;
        }
      }
      
      if (authToken) {
        return authToken;
      } else {
        console.log('在cookie中未找到Authorization');
      }
    }
    
    // 从storage中获取token配置
    chrome.storage.local.get({
      tokenSource: 'auto',
      tokenKey: 'token',
      tokenSelector: ''
    }, function(config) {
      // 根据配置的来源提取token
      if (config.tokenSource === 'localStorage' || config.tokenSource === 'auto') {
        // 从localStorage中提取token
        const key = config.tokenKey || 'token';
        const token = localStorage.getItem(key);
        
        if (token) {
          return token;
        }
      }
      
      if (config.tokenSource === 'cookie' || config.tokenSource === 'auto') {
        // 从cookie中提取token
        const cookies = document.cookie.split(';');
        const key = config.tokenKey || 'token';
        let tokenFromCookie = null;
        
        for (const cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === key || name === 'auth_token' || name === 'Authorization') {
            tokenFromCookie = value;
            break;
          }
        }
        
        if (tokenFromCookie) {
          return tokenFromCookie;
        }
      }
      
      if (config.tokenSource === 'element' || config.tokenSource === 'auto') {
        // 从页面元素中提取token
        const selector = config.tokenSelector || '[data-token]';
        const tokenElement = document.querySelector(selector);
        
        if (tokenElement) {
          if (tokenElement.dataset && tokenElement.dataset.token) {
            return tokenElement.dataset.token;
          }
          return tokenElement.textContent.trim();
        }
      }
    });
    
    // 默认提取逻辑
    // 从localStorage中提取token
    const token = localStorage.getItem('token');
    
    if (token) {
      return token;
    }
    
    // 从cookie中提取token
    const cookies = document.cookie.split(';');
    let tokenFromCookie = null;
    
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'token' || name === 'auth_token' || name === 'Authorization') {
        tokenFromCookie = value;
        break;
      }
    }
    
    if (tokenFromCookie) {
      return tokenFromCookie;
    }
    
    // 从页面元素中提取token
    // 这需要根据实际网站结构调整
    const tokenElement = document.querySelector('[data-token]');
    
    if (tokenElement && tokenElement.dataset.token) {
      return tokenElement.dataset.token;
    }
    
    // 尝试从其他可能的来源提取
    // 例如，从全局变量中提取
    if (window.TOKEN || window.token) {
      return window.TOKEN || window.token;
    }
    
    return null;
  } catch (error) {
    console.error('提取token出错:', error);
    return null;
  }
}

// 从1688页面中提取商品信息的函数
function extractProductInfo() {
  try {
    // 尝试从window.context.result.data获取商品信息
    if (window.context && window.context.result && window.context.result.data) {
      console.log('成功从window.context.result.data获取商品信息');
      const contextData = window.context.result.data;
      
      // 提取商品信息
      const product = parseContextData(contextData);
      
      // 转换为ProductParam对象
      const productParam = convertToProductParam(product);
      
      return productParam;
    } else {
      console.log('window.context.result.data不存在，尝试使用传统方式获取商品信息');
      
      // 检查是否是商品详情页
      const isDetailPage = checkIsDetailPage();
      if (!isDetailPage) {
        console.error('当前页面不是商品详情页');
        alert('当前页面不是商品详情页，无法获取商品信息');
        return null;
      }
      
      // 使用传统方式提取商品信息
      const product = {
        id: getProductId(),
        title: getProductTitle(),
        price: getProductPrice(),
        oriPrice: getProductPrice(), // 默认原价等于现价
        images: getProductImages(),
        description: getProductDescription(),
        specs: getProductSpecs(),
        seller: getSellerInfo(),
        url: window.location.href,
        // 构造ProductParam对象所需的额外信息
        totalStocks: 99, // 默认库存
        brief: getProductDescription().substring(0, 100), // 简短描述
        content: generateProductContentFromDOM(), // 从DOM生成详情内容
        status: 1, // 默认上架状态
        categoryId: 85, // 默认分类ID
        deliveryModeVo: {
          hasUserPickUp: false,
          hasShopDelivery: true
        }
      };
      
      // 转换为ProductParam对象
      const productParam = convertToProductParam(product);
      
      return productParam;
    }
  } catch (error) {
    console.error('提取商品信息出错:', error);
    return null;
  }
  
  // 解析window.context.result.data数据
  function parseContextData(contextData) {
    try {
      // 提取基本信息
      const rootFields = contextData.Root && contextData.Root.fields ? contextData.Root.fields : {};
      const dataJson = rootFields.dataJson || {};
      const tempModel = dataJson.tempModel || {};
      const skuModel = dataJson.skuModel || {};
      
      // 提取商品标题
      const title = tempModel.offerTitle || 
                   (contextData.productTitle && contextData.productTitle.fields ? contextData.productTitle.fields.title : '') || 
                   (contextData.gallery && contextData.gallery.fields ? contextData.gallery.fields.subject : '');
      
      // 提取商品ID
      const id = tempModel.offerId || dataJson.offerBaseInfo?.offerId || '';
      
      // 提取商品价格
      const priceInfo = contextData.mainPrice && contextData.mainPrice.fields ? contextData.mainPrice.fields : {};
      const priceModel = priceInfo.priceModel || {};
      const originalPrices = priceModel.originalPrices || [];
      const currentPrices = priceModel.currentPrices || [];
      
      // 获取最低价格
      let price = 0;
      let oriPrice = 0;
      
      if (currentPrices.length > 0) {
        price = parseFloat(currentPrices[0].price) || 0;
      }
      
      if (originalPrices.length > 0) {
        oriPrice = parseFloat(originalPrices[0].price) || price;
      }
      
      // 提取商品图片
      const images = [];
      if (contextData.gallery && contextData.gallery.fields && contextData.gallery.fields.offerImgList) {
        images.push(...contextData.gallery.fields.offerImgList);
      } else if (dataJson.images && dataJson.images.length > 0) {
        dataJson.images.forEach(img => {
          if (img.fullPathImageURI) {
            images.push(img.fullPathImageURI);
          }
        });
      }
      
      // 提取商品描述
      const description = contextData.description && contextData.description.fields ? 
                         contextData.description.fields.detailUrl || '' : '';
      
      // 提取卖家信息
      const seller = tempModel.companyName || dataJson.offerBaseInfo?.sellerLoginId || '';
      
      // 提取SKU信息
      const specs = {};
      const skuInfoMap = {};
      const skuList = [];
      
      // 处理SKU信息
      if (skuModel.skuProps && skuModel.skuProps.length > 0) {
        const skuProps = skuModel.skuProps;
        
        // 提取规格属性
        skuProps.forEach(prop => {
          if (prop.value && prop.value.length > 0) {
            specs[prop.prop] = prop.value.map(val => val.name);
          }
        });
        
        // 提取SKU详情
        if (skuModel.skuInfoMap) {
          for (const key in skuModel.skuInfoMap) {
            const sku = skuModel.skuInfoMap[key];
            skuList.push({
              skuId: sku.skuId,
              properties: key,
              price: parseFloat(sku.discountPrice) || parseFloat(sku.price) || 0,
              originalPrice: parseFloat(sku.price) || 0,
              stocks: sku.canBookCount || 99
            });
          }
        }
      }
      
      // 提取库存信息
      let totalStocks = 99;
      if (dataJson.orderParamModel && dataJson.orderParamModel.orderParam) {
        totalStocks = dataJson.orderParamModel.orderParam.canBookedAmount || 99;
      }
      
      // 提取商品重量信息
      const weightInfo = {};
      if (contextData.productPackInfo && contextData.productPackInfo.fields && 
          contextData.productPackInfo.fields.pieceWeightScale && 
          contextData.productPackInfo.fields.pieceWeightScale.pieceWeightScaleInfo) {
        
        const pieceWeightScaleInfo = contextData.productPackInfo.fields.pieceWeightScale.pieceWeightScaleInfo;
        pieceWeightScaleInfo.forEach(item => {
          weightInfo[item.sku1] = {
            weight: item.weight,
            length: item.length,
            width: item.width,
            height: item.height,
            volume: item.volume,
            skuId: item.skuId
          };
        });
      }
      
      // 构造商品信息对象
      return {
        id: id,
        title: title,
        price: price,
        oriPrice: oriPrice,
        images: images,
        description: description,
        specs: specs,
        skuList: skuList,
        weightInfo: weightInfo,
        seller: seller,
        url: window.location.href,
        totalStocks: totalStocks,
        brief: title.substring(0, 200), // 使用标题作为简短描述
        content: generateProductContentFromContextData(contextData),
        status: 1, // 默认上架状态
        categoryId: 85, // 默认分类ID
        deliveryModeVo: {
          hasUserPickUp: false,
          hasShopDelivery: true
        }
      };
    } catch (error) {
      console.error('解析contextData出错:', error);
      return {
        id: getProductId(),
        title: getProductTitle(),
        price: getProductPrice(),
        oriPrice: getProductPrice(),
        images: getProductImages(),
        description: getProductDescription(),
        specs: getProductSpecs(),
        seller: getSellerInfo(),
        url: window.location.href,
        totalStocks: 99,
        brief: getProductDescription().substring(0, 100),
        content: generateProductContentFromDOM(),
        status: 1,
        categoryId: 85,
        deliveryModeVo: {
          hasUserPickUp: false,
          hasShopDelivery: true
        }
      };
    }
  }
  
  // 从contextData生成商品详情内容
  function generateProductContentFromContextData(contextData) {
    let content = '<p>';
    
    // 添加商品图片
    if (contextData.gallery && contextData.gallery.fields && contextData.gallery.fields.offerImgList) {
      contextData.gallery.fields.offerImgList.forEach(img => {
        content += `<img src="${img}" alt="" width="800" height="800" />\n`;
      });
    }
    
    // 添加商品描述
    if (contextData.description && contextData.description.fields && contextData.description.fields.detailUrl) {
      content += `<p>详情链接: ${contextData.description.fields.detailUrl}</p>\n`;
    }
    
    // 添加规格信息
    if (contextData.productPackInfo && contextData.productPackInfo.fields && 
        contextData.productPackInfo.fields.pieceWeightScale && 
        contextData.productPackInfo.fields.pieceWeightScale.pieceWeightScaleInfo) {
      
      content += '<p><strong>规格参数：</strong></p>\n<ul>\n';
      
      const pieceWeightScaleInfo = contextData.productPackInfo.fields.pieceWeightScale.pieceWeightScaleInfo;
      pieceWeightScaleInfo.forEach(item => {
        content += `<li>${item.sku1}: 重量${item.weight}g, 尺寸${item.length}x${item.width}x${item.height}cm</li>\n`;
      });
      
      content += '</ul>\n';
    }
    
    // 添加服务信息
    if (contextData.mainServices && contextData.mainServices.fields && contextData.mainServices.fields.guaranteeList) {
      content += '<p><strong>服务：</strong></p>\n<ul>\n';
      
      contextData.mainServices.fields.guaranteeList.forEach(service => {
        content += `<li>${service.serviceName}: ${service.description}</li>\n`;
      });
      
      content += '</ul>\n';
    }
    
    content += '</p>';
    return content;
  }
  
  // 检查是否是商品详情页
  function checkIsDetailPage() {
    // 检查URL是否包含商品ID
    const hasProductIdInUrl = /\/(\d+)\.html/.test(window.location.href);
    
    // 检查是否有商品标题元素
    const hasTitleElement = !!document.querySelector('.title-text') || 
                          !!document.querySelector('.title') || 
                          !!document.querySelector('h1');
    
    // 检查是否有价格元素
    const hasPriceElement = !!document.querySelector('.price-text') || 
                          !!document.querySelector('.price') ||
                          !!document.querySelector('[class*="price"]');
    
    return hasProductIdInUrl && (hasTitleElement || hasPriceElement);
  }
  
  // 获取商品ID
  function getProductId() {
    // 从URL中提取商品ID
    const match = window.location.href.match(/\/(\d+)\.html/);
    return match ? match[1] : null;
  }
  
  // 获取商品标题
  function getProductTitle() {
    const titleElement = document.querySelector('.title-text') || 
                        document.querySelector('.title') || 
                        document.querySelector('h1');
    return titleElement ? titleElement.textContent.trim() : '未知标题';
  }
  
  // 获取商品价格
  function getProductPrice() {
    const priceElement = document.querySelector('.price-text') || 
                        document.querySelector('.price') ||
                        document.querySelector('[class*="price"]');
    
    if (priceElement) {
      // 提取价格文本，去除非数字和小数点字符
      const priceText = priceElement.textContent.trim();
      const priceMatch = priceText.match(/[\d\.]+/);
      return priceMatch ? parseFloat(priceMatch[0]) : 0;
    }
    
    return 0;
  }
  
  // 获取商品图片
  function getProductImages() {
    const images = [];
    const imageElements = document.querySelectorAll('.detail-gallery-img img, .detail-gallery img, [class*="gallery"] img');
    
    imageElements.forEach(img => {
      if (img.src) {
        images.push(img.src);
      }
    });
    
    return images;
  }
  
  // 获取商品描述
  function getProductDescription() {
    const descElement = document.querySelector('.detail-desc') || 
                       document.querySelector('.desc') ||
                       document.querySelector('[class*="description"]');
    return descElement ? descElement.textContent.trim() : '暂无描述';
  }
  
  // 获取商品规格
  function getProductSpecs() {
    const specs = {};
    const specElements = document.querySelectorAll('.detail-attr-item, .attr-item, [class*="spec"]');
    
    specElements.forEach(spec => {
      const key = spec.querySelector('.attr-name, .name')?.textContent.trim();
      const value = spec.querySelector('.attr-value, .value')?.textContent.trim();
      
      if (key && value) {
        specs[key] = value;
      }
    });
    
    return specs;
  }
  
  // 获取卖家信息
  function getSellerInfo() {
    const sellerElement = document.querySelector('.seller-name') || 
                         document.querySelector('.company-name') ||
                         document.querySelector('[class*="seller"]');
    
    return sellerElement ? sellerElement.textContent.trim() : '未知卖家';
  }
  
  // 从DOM生成商品详情内容
  function generateProductContentFromDOM() {
    let content = '<p>';
    
    // 添加商品图片
    const images = getProductImages();
    if (images.length > 0) {
      images.forEach(img => {
        content += `<img src="${img}" alt="${getProductTitle()}" width="800" height="800" />\n`;
      });
    }
    
    // 添加商品描述
    const description = getProductDescription();
    if (description) {
      content += `<p>${description}</p>\n`;
    }
    
    // 添加规格信息
    const specs = getProductSpecs();
    if (Object.keys(specs).length > 0) {
      content += '<p><strong>规格参数：</strong></p>\n<ul>\n';
      for (const key in specs) {
        content += `<li>${key}: ${specs[key]}</li>\n`;
      }
      content += '</ul>\n';
    }
    
    content += '</p>';
    return content;
  }
  
  // 转换为ProductParam对象
  function convertToProductParam(product) {
    // 处理图片路径，转换为相对路径
    const processImagePath = (url) => {
      // 这里可以根据实际情况处理图片路径
      // 示例：将完整URL转换为相对路径格式
      const fileName = url.split('/').pop();
      return `meyu/${new Date().getFullYear()}/${String(new Date().getMonth() + 1).padStart(2, '0')}/${fileName}`;
    };
    
    // 处理第一张图片作为主图
    const mainPic = product.images && product.images.length > 0 
      ? processImagePath(product.images[0]) 
      : '';
    
    // 处理所有图片作为轮播图
    const imgs = product.images && product.images.length > 0 
      ? product.images.map(img => processImagePath(img)).join(',')
      : mainPic;
    
    // 构造SKU列表
    let skuList = [];
    
    // 如果产品已经有处理好的skuList，直接使用
    if (product.skuList && product.skuList.length > 0) {
      skuList = product.skuList.map(sku => {
        return {
          skuId: sku.skuId || 1,
          properties: sku.properties || '默认:默认',
          originalPrice: sku.originalPrice || product.oriPrice || 0,
          price: sku.price || product.price || 0,
          stocks: sku.stocks || Math.floor(product.totalStocks / product.skuList.length)
        };
      });
    } 
    // 否则从规格中构造SKU
    else if (product.specs && typeof product.specs === 'object') {
      let skuId = 1;
      
      // 如果规格是数组的形式
      if (Array.isArray(product.specs)) {
        product.specs.forEach(spec => {
          skuList.push({
            skuId: skuId++,
            properties: `${spec}:${spec}`,
            originalPrice: product.oriPrice,
            price: product.price,
            stocks: Math.floor(product.totalStocks / product.specs.length)
          });
        });
      } 
      // 如果规格是对象的形式
      else {
        for (const key in product.specs) {
          // 如果规格值是数组
          if (Array.isArray(product.specs[key])) {
            product.specs[key].forEach(val => {
              skuList.push({
                skuId: skuId++,
                properties: `${key}:${val}`,
                originalPrice: product.oriPrice,
                price: product.price,
                stocks: Math.floor(product.totalStocks / (Object.keys(product.specs).length * product.specs[key].length))
              });
            });
          } 
          // 如果规格值是字符串
          else {
            skuList.push({
              skuId: skuId++,
              properties: `${key}:${product.specs[key]}`,
              originalPrice: product.oriPrice,
              price: product.price,
              stocks: Math.floor(product.totalStocks / Object.keys(product.specs).length)
            });
          }
        }
      }
      
      // 如果没有规格，添加一个默认SKU
      if (skuList.length === 0) {
        skuList.push({
          skuId: 1,
          properties: '默认:默认',
          originalPrice: product.oriPrice,
          price: product.price,
          stocks: product.totalStocks
        });
      }
    } else {
      // 添加一个默认SKU
      skuList.push({
        skuId: 1,
        properties: '默认:默认',
        originalPrice: product.oriPrice,
        price: product.price,
        stocks: product.totalStocks
      });
    }
    
    // 构造ProductParam对象
    return {
      prodId: parseInt(product.id) || null,
      status: product.status || 1,
      prodName: product.title || '未命名商品',
      price: product.price || 0,
      oriPrice: product.oriPrice || product.price || 0,
      totalStocks: product.totalStocks || 99,
      brief: product.brief || product.description || '',
      pic: mainPic,
      imgs: imgs,
      categoryId: product.categoryId || 85,
      skuList: skuList,
      content: product.content || '',
      deliveryModeVo: product.deliveryModeVo || {
        hasUserPickUp: false,
        hasShopDelivery: true
      },
      deliveryTemplateId: 47, // 默认运费模板ID
      tagList: [] // 默认标签列表为空
    };
  }
}

// 在1688页面中执行购买操作的函数
async function performBuyAction(product, token) {
  try {
    // 添加token到请求头
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      options.headers = options.headers || {};
      options.headers['Authorization'] = `Bearer ${token}`;
      return originalFetch.call(this, url, options);
    };
    
    // 选择商品规格（如果有）
    await selectProductSpecs();
    
    // 设置购买数量
    await setQuantity(1);
    
    // 点击购买按钮
    await clickBuyButton();
    
    // 返回订单信息
    return {
      orderId: 'ORD' + Date.now(),
      product: product.title,
      price: product.price,
      time: new Date().toISOString()
    };
  } catch (error) {
    console.error('购买商品出错:', error);
    throw error;
  }
  
  // 选择商品规格
  async function selectProductSpecs() {
    const specItems = document.querySelectorAll('.sku-item, .sku-property-item, [class*="sku"]');
    
    if (specItems.length === 0) {
      return; // 没有规格可选
    }
    
    // 选择第一个可用的规格
    for (const item of specItems) {
      if (!item.classList.contains('disabled') && !item.classList.contains('selected')) {
        item.click();
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }
  
  // 设置购买数量
  async function setQuantity(num) {
    const quantityInput = document.querySelector('.amount-input, input[name="quantity"], [class*="quantity"]');
    
    if (quantityInput) {
      // 清除原有值
      quantityInput.value = '';
      
      // 设置新值
      quantityInput.value = num;
      
      // 触发change事件
      const event = new Event('change', { bubbles: true });
      quantityInput.dispatchEvent(event);
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }
  
  // 点击购买按钮
  async function clickBuyButton() {
    const buyButton = document.querySelector('.do-buy, .buy-now-btn, [class*="buy"]');
    
    if (buyButton) {
      buyButton.click();
      await new Promise(resolve => setTimeout(resolve, 1000));
    } else {
      throw new Error('未找到购买按钮');
    }
  }
}

// 从拼多多页面中提取商品信息的函数
function extractPddProductInfo() {
  try {
    // 提取商品信息
    const product = {
      id: getPddProductId(),
      title: getPddProductTitle(),
      price: getPddProductPrice(),
      images: getPddProductImages(),
      description: getPddProductDescription(),
      specs: getPddProductSpecs(),
      seller: getPddSellerInfo(),
      url: window.location.href
    };
    
    return product;
  } catch (error) {
    console.error('提取拼多多商品信息出错:', error);
    return null;
  }
  
  // 获取商品ID
  function getPddProductId() {
    // 从URL中提取商品ID
    const match = window.location.href.match(/goods_id=(\d+)/);
    return match ? match[1] : null;
  }
  
  // 获取商品标题
  function getPddProductTitle() {
    const titleElement = document.querySelector('.goods-name') || 
                        document.querySelector('.detail-title') || 
                        document.querySelector('h2.title');
    return titleElement ? titleElement.textContent.trim() : '未知标题';
  }
  
  // 获取商品价格
  function getPddProductPrice() {
    const priceElement = document.querySelector('.price') || 
                        document.querySelector('.now-price') ||
                        document.querySelector('[class*="price"]');
    return priceElement ? priceElement.textContent.trim() : '未知价格';
  }
  
  // 获取商品图片
  function getPddProductImages() {
    const images = [];
    const imageElements = document.querySelectorAll('.carousel-inner img, .preview-img img, [class*="gallery"] img');
    
    imageElements.forEach(img => {
      if (img.src) {
        images.push(img.src);
      }
    });
    
    return images;
  }
  
  // 获取商品描述
  function getPddProductDescription() {
    const descElement = document.querySelector('.goods-desc') || 
                       document.querySelector('.detail-desc') ||
                       document.querySelector('[class*="description"]');
    return descElement ? descElement.textContent.trim() : '暂无描述';
  }
  
  // 获取商品规格
  function getPddProductSpecs() {
    const specs = {};
    const specElements = document.querySelectorAll('.sku-spec-item, .sku-item, [class*="spec"]');
    
    specElements.forEach(spec => {
      const key = spec.querySelector('.name, .spec-name')?.textContent.trim();
      const value = spec.querySelector('.value, .spec-value')?.textContent.trim();
      
      if (key && value) {
        specs[key] = value;
      }
    });
    
    return specs;
  }
  
  // 获取卖家信息
  function getPddSellerInfo() {
    const sellerElement = document.querySelector('.store-name') || 
                         document.querySelector('.shop-name') ||
                         document.querySelector('[class*="seller"], [class*="store"]');
    
    return sellerElement ? sellerElement.textContent.trim() : '未知卖家';
  }
}

// 检查当前URL是否匹配指定模式
function isUrlMatched(url, patterns) {
  if (!patterns || !patterns.length) {
    return false;
  }
  
  return patterns.some(pattern => {
    // 将通配符模式转换为正则表达式
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*');
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(url);
  });
}

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  // 处理来自后台脚本的消息
  if (request.action === 'extractToken') {
    // 从页面中提取token
    const token = extractToken();
    sendResponse({ token });
  }
  else if (request.action === 'extractProductInfo') {
    // 从1688页面中提取商品信息
    const product = extractProductInfo();
    sendResponse({ product });
  }
  else if (request.action === 'performBuyAction') {
    // 在1688页面中执行购买操作
    performBuyAction(request.product, request.token)
      .then(result => {
        sendResponse({ success: true, orderInfo: result });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // 异步响应
  }
  else if (request.action === 'showFloatingModule') {
    // 显示悬浮模块
    createFloatingModule();
    sendResponse({ success: true });
  }
});

// 根据配置决定是否自动显示悬浮模块
chrome.storage.local.get({
  showFloatingModule: true,
  tokenSites: ['http://localhost:952/*']
}, function(result) {
  if (result.showFloatingModule) {
    // 检查当前页面是否需要显示悬浮模块
    const is1688Site = window.location.href.includes('1688.com');
    const isLocalhost952 = window.location.href.includes('localhost:952');
    const isTokenSite = isUrlMatched(window.location.href, result.tokenSites);
    
    if (is1688Site || isTokenSite || isLocalhost952) {
      // 等待页面加载完成后显示悬浮模块
      if (document.readyState === 'complete') {
        createFloatingModule();
      } else {
        window.addEventListener('load', createFloatingModule);
      }
    }
  }
}); 