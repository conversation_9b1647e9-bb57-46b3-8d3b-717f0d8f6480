<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Mall Token Extension</title>
  <link rel="stylesheet" href="css/popup.css">
</head>
<body>
  <div class="container">
    <h1>Mall Token Extension</h1>
    
    <div class="section">
      <h2>Token 设置</h2>
      <div class="form-group">
        <label for="tokenUrl">Token 获取网址:</label>
        <input type="text" id="tokenUrl" placeholder="输入获取token的网址">
      </div>
      <div class="form-group">
        <button id="saveSettings">保存设置</button>
      </div>
      <div id="tokenStatus" class="status">
        Token 状态: <span id="tokenStatusText">未获取</span>
      </div>
    </div>

    <div class="section">
      <h2>1688采集</h2>
      <div class="form-group">
        <label for="productUrl">1688商品网址:</label>
        <input type="text" id="productUrl" placeholder="输入1688商品网址">
      </div>
      <div class="form-group">
        <button id="collectProduct">采集商品</button>
      </div>
      <div id="collectStatus" class="status">
        采集状态: <span id="collectStatusText">未开始</span>
      </div>
    </div>

    <div class="section">
      <h2>购买功能</h2>
      <div class="form-group">
        <button id="buyProduct" disabled>购买商品</button>
      </div>
      <div id="buyStatus" class="status">
        购买状态: <span id="buyStatusText">未开始</span>
      </div>
    </div>
  </div>

  <script src="js/popup.js"></script>
</body>
</html> 