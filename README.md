# Mall Token Extension

这是一个Chrome扩展，用于从指定网址获取token，然后去1688采集商品信息和购买商品。

## 功能

- 从指定网址获取token（支持从localStorage、cookie或页面元素中提取）
- 使用token从1688网站采集商品信息
- 使用token在1688网站购买商品

## 安装方法

1. 下载或克隆本仓库
2. 打开Chrome浏览器，在地址栏输入 `chrome://extensions/`
3. 打开右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择本扩展的目录

## 使用方法

1. 点击Chrome工具栏中的扩展图标，打开弹出页面
2. 在"Token 设置"部分，输入获取token的网址，点击"保存设置"
3. 扩展会自动访问该网址并尝试获取token
4. 获取token成功后，在"1688采集"部分输入1688商品网址，点击"采集商品"
5. 采集成功后，可以点击"购买商品"按钮进行购买

## 调试方法

1. 在扩展管理页面找到本扩展，点击"查看视图：背景页"链接，打开后台页面的开发者工具
2. 在开发者工具中可以查看控制台输出、设置断点等
3. 右键点击扩展图标，选择"检查弹出内容"，可以调试弹出页面
4. 在目标网页上打开开发者工具（F12），可以调试内容脚本

## 图标生成

如果需要生成不同尺寸的图标，可以按照以下步骤操作：

1. 安装依赖：`npm install`
2. 运行图标生成脚本：`npm run generate-icons`

## 注意事项

- 本扩展需要访问指定网址和1688网站，请确保有相应的权限
- token获取逻辑可能需要根据实际网站结构进行调整
- 1688商品页面结构可能会变化，可能需要更新相应的选择器 