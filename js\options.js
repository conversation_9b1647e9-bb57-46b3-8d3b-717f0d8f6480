// 当DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const showFloatingModule = document.getElementById('showFloatingModule');
  const tokenSites = document.getElementById('tokenSites');
  const tokenSelector = document.getElementById('tokenSelector');
  const tokenSource = document.getElementById('tokenSource');
  const tokenKey = document.getElementById('tokenKey');
  const autoCollect = document.getElementById('autoCollect');
  const collectDelay = document.getElementById('collectDelay');
  const defaultQuantity = document.getElementById('defaultQuantity');
  const confirmPurchase = document.getElementById('confirmPurchase');
  const saveButton = document.getElementById('saveOptions');
  const statusDiv = document.getElementById('status');
  
  // 加载保存的设置
  loadOptions();
  
  // 保存按钮点击事件
  saveButton.addEventListener('click', function() {
    // 处理tokenSites，将文本框中的内容按行分割成数组
    const tokenSitesArray = tokenSites.value
      .split('\n')
      .map(site => site.trim())
      .filter(site => site.length > 0);
    
    // 保存设置
    chrome.storage.local.set({
      showFloatingModule: showFloatingModule.value === 'true',
      tokenSites: tokenSitesArray,
      tokenSelector: tokenSelector.value,
      tokenSource: tokenSource.value,
      tokenKey: tokenKey.value,
      autoCollect: autoCollect.value === 'true',
      collectDelay: parseInt(collectDelay.value) || 2000,
      defaultQuantity: parseInt(defaultQuantity.value) || 1,
      confirmPurchase: confirmPurchase.value === 'true'
    }, function() {
      // 显示保存成功消息
      showStatus('设置已保存', 'success');
    });
  });
  
  // 加载保存的设置
  function loadOptions() {
    chrome.storage.local.get({
      // 默认值
      showFloatingModule: true,
      tokenSites: ['http://localhost:952/*'], // 默认添加localhost:952
      tokenSelector: '',
      tokenSource: 'cookie', // 默认从cookie中获取
      tokenKey: 'Authorization', // 默认键名为Authorization
      autoCollect: false,
      collectDelay: 2000,
      defaultQuantity: 1,
      confirmPurchase: true
    }, function(items) {
      // 设置表单值
      showFloatingModule.value = items.showFloatingModule.toString();
      tokenSites.value = items.tokenSites.join('\n');
      tokenSelector.value = items.tokenSelector;
      tokenSource.value = items.tokenSource;
      tokenKey.value = items.tokenKey;
      autoCollect.value = items.autoCollect.toString();
      collectDelay.value = items.collectDelay;
      defaultQuantity.value = items.defaultQuantity;
      confirmPurchase.value = items.confirmPurchase.toString();
    });
  }
  
  // 显示状态消息
  function showStatus(message, type) {
    statusDiv.textContent = message;
    statusDiv.className = 'status ' + type;
    statusDiv.style.display = 'block';
    
    // 3秒后隐藏消息
    setTimeout(function() {
      statusDiv.style.display = 'none';
    }, 3000);
  }
}); 