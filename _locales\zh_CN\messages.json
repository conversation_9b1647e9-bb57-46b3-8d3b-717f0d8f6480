{"appName": {"message": "Mall Token Extension", "description": "扩展的名称"}, "appDesc": {"message": "从指定网址获取token，然后去1688采集商品信息", "description": "扩展的描述"}, "description": {"message": "从指定网址获取token，然后去1688采集商品信息和购买商品", "description": "扩展的详细描述"}, "tokenSettings": {"message": "Token 设置", "description": "Token设置标题"}, "tokenUrl": {"message": "Token 获取网址:", "description": "Token获取网址标签"}, "saveSettings": {"message": "保存设置", "description": "保存设置按钮"}, "tokenStatus": {"message": "Token 状态:", "description": "Token状态标签"}, "notAcquired": {"message": "未获取", "description": "未获取状态"}, "acquired": {"message": "已获取", "description": "已获取状态"}, "acquiring": {"message": "获取中...", "description": "获取中状态"}, "acquisitionFailed": {"message": "获取失败", "description": "获取失败状态"}, "collectionSettings": {"message": "1688采集", "description": "1688采集标题"}, "productUrl": {"message": "1688商品网址:", "description": "1688商品网址标签"}, "collectProduct": {"message": "采集商品", "description": "采集商品按钮"}, "collectionStatus": {"message": "采集状态:", "description": "采集状态标签"}, "notStarted": {"message": "未开始", "description": "未开始状态"}, "collecting": {"message": "采集中...", "description": "采集中状态"}, "collectionSucceeded": {"message": "采集成功", "description": "采集成功状态"}, "collectionFailed": {"message": "采集失败", "description": "采集失败状态"}, "purchaseSettings": {"message": "购买功能", "description": "购买功能标题"}, "buyProduct": {"message": "购买商品", "description": "购买商品按钮"}, "purchaseStatus": {"message": "购买状态:", "description": "购买状态标签"}, "purchasing": {"message": "购买中...", "description": "购买中状态"}, "purchaseSucceeded": {"message": "购买成功", "description": "购买成功状态"}, "purchaseFailed": {"message": "购买失败", "description": "购买失败状态"}, "pleaseEnterTokenUrl": {"message": "请输入获取token的网址", "description": "请输入获取token的网址提示"}, "pleaseEnterProductUrl": {"message": "请输入1688商品网址", "description": "请输入1688商品网址提示"}, "pleaseAcquireTokenFirst": {"message": "请先获取token", "description": "请先获取token提示"}, "pleaseCollectProductFirst": {"message": "请先获取token并采集商品", "description": "请先获取token并采集商品提示"}}