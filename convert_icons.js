// 提示用户手动转换SVG到PNG
console.log('请按照以下步骤手动转换SVG到PNG图标：');
console.log('1. 打开在线SVG到PNG转换工具，如 https://svgtopng.com/');
console.log('2. 上传 images/icon.svg 文件');
console.log('3. 分别设置以下尺寸并下载：');
console.log('   - 16x16 像素，保存为 images/icon16.png');
console.log('   - 48x48 像素，保存为 images/icon48.png');
console.log('   - 128x128 像素，保存为 images/icon128.png');
console.log('4. 将下载的PNG文件放到扩展的images目录中');

// 或者使用以下命令行工具（需要安装）：
console.log('\n或者，如果您安装了ImageMagick，可以使用以下命令：');
console.log('convert -background none -resize 16x16 images/icon.svg images/icon16.png');
console.log('convert -background none -resize 48x48 images/icon.svg images/icon48.png');
console.log('convert -background none -resize 128x128 images/icon.svg images/icon128.png'); 