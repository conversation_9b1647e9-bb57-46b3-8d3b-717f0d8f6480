document.addEventListener('DOMContentLoaded', function() {
  // 获取DOM元素
  const tokenUrlInput = document.getElementById('tokenUrl');
  const saveSettingsBtn = document.getElementById('saveSettings');
  const tokenStatusText = document.getElementById('tokenStatusText');
  
  const productUrlInput = document.getElementById('productUrl');
  const collectProductBtn = document.getElementById('collectProduct');
  const collectStatusText = document.getElementById('collectStatusText');
  
  const buyProductBtn = document.getElementById('buyProduct');
  const buyStatusText = document.getElementById('buyStatusText');
  
  // 加载保存的设置
  chrome.storage.local.get(['tokenUrl', 'token'], function(result) {
    if (result.tokenUrl) {
      tokenUrlInput.value = result.tokenUrl;
    }
    
    if (result.token) {
      tokenStatusText.textContent = '已获取';
      tokenStatusText.classList.add('success');
    }
  });
  
  // 保存设置
  saveSettingsBtn.addEventListener('click', function() {
    const tokenUrl = tokenUrlInput.value.trim();
    
    if (!tokenUrl) {
      alert('请输入获取token的网址');
      return;
    }
    
    // 保存设置
    chrome.storage.local.set({ tokenUrl }, function() {
      // 发送消息给后台脚本，获取token
      tokenStatusText.textContent = '获取中...';
      tokenStatusText.className = 'loading';
      
      chrome.runtime.sendMessage({ action: 'getToken', url: tokenUrl }, function(response) {
        if (response && response.success) {
          tokenStatusText.textContent = '已获取';
          tokenStatusText.className = 'success';
        } else {
          tokenStatusText.textContent = '获取失败';
          tokenStatusText.className = 'error';
        }
      });
    });
  });
  
  // 采集商品
  collectProductBtn.addEventListener('click', function() {
    const productUrl = productUrlInput.value.trim();
    
    if (!productUrl) {
      alert('请输入1688商品网址');
      return;
    }
    
    // 检查是否有token
    chrome.storage.local.get('token', function(result) {
      if (!result.token) {
        alert('请先获取token');
        return;
      }
      
      collectStatusText.textContent = '采集中...';
      collectStatusText.className = 'loading';
      
      // 发送消息给后台脚本，采集商品
      chrome.runtime.sendMessage({ 
        action: 'collectProduct', 
        url: productUrl,
        token: result.token
      }, function(response) {
        if (response && response.success) {
          collectStatusText.textContent = '采集成功';
          collectStatusText.className = 'success';
          buyProductBtn.disabled = false;
        } else {
          collectStatusText.textContent = '采集失败';
          collectStatusText.className = 'error';
        }
      });
    });
  });
  
  // 购买商品
  buyProductBtn.addEventListener('click', function() {
    // 检查是否有token和已采集的商品
    chrome.storage.local.get(['token', 'collectedProduct'], function(result) {
      if (!result.token || !result.collectedProduct) {
        alert('请先获取token并采集商品');
        return;
      }
      
      buyStatusText.textContent = '购买中...';
      buyStatusText.className = 'loading';
      
      // 发送消息给后台脚本，购买商品
      chrome.runtime.sendMessage({ 
        action: 'buyProduct', 
        product: result.collectedProduct,
        token: result.token
      }, function(response) {
        if (response && response.success) {
          buyStatusText.textContent = '购买成功';
          buyStatusText.className = 'success';
        } else {
          buyStatusText.textContent = '购买失败';
          buyStatusText.className = 'error';
        }
      });
    });
  });
}); 