<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Mall Token Extension 设置</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      color: #1890ff;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
      margin-top: 0;
    }
    
    .section {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 4px;
      border: 1px solid #eee;
    }
    
    h2 {
      margin-top: 0;
      color: #333;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input[type="text"], 
    input[type="number"], 
    select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    
    button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #40a9ff;
    }
    
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
    }
    
    .success {
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #52c41a;
    }
    
    .error {
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      color: #f5222d;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Mall Token Extension 高级设置</h1>
    
    <div class="section">
      <h2>界面设置</h2>
      <div class="form-group">
        <label for="showFloatingModule">在指定页面显示悬浮模块:</label>
        <select id="showFloatingModule">
          <option value="true">开启</option>
          <option value="false">关闭</option>
        </select>
      </div>
      <div class="form-group">
        <label for="tokenSites">Token获取网站 (每行一个URL，支持通配符):</label>
        <textarea id="tokenSites" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;" placeholder="例如: https://example.com/login&#10;https://*.example.org/*"></textarea>
      </div>
    </div>
    
    <div class="section">
      <h2>Token 设置</h2>
      <div class="form-group">
        <label for="tokenSelector">Token 提取选择器:</label>
        <input type="text" id="tokenSelector" placeholder="例如: [data-token], #token">
      </div>
      <div class="form-group">
        <label for="tokenSource">Token 来源:</label>
        <select id="tokenSource">
          <option value="auto">自动检测</option>
          <option value="localStorage">localStorage</option>
          <option value="cookie">Cookie</option>
          <option value="element">页面元素</option>
        </select>
      </div>
      <div class="form-group">
        <label for="tokenKey">Token 键名 (localStorage/Cookie):</label>
        <input type="text" id="tokenKey" placeholder="例如: token, auth_token">
      </div>
    </div>
    
    <div class="section">
      <h2>1688 采集设置</h2>
      <div class="form-group">
        <label for="autoCollect">自动采集:</label>
        <select id="autoCollect">
          <option value="false">关闭</option>
          <option value="true">开启</option>
        </select>
      </div>
      <div class="form-group">
        <label for="collectDelay">采集延迟 (毫秒):</label>
        <input type="number" id="collectDelay" min="0" value="2000">
      </div>
    </div>
    
    <div class="section">
      <h2>购买设置</h2>
      <div class="form-group">
        <label for="defaultQuantity">默认购买数量:</label>
        <input type="number" id="defaultQuantity" min="1" value="1">
      </div>
      <div class="form-group">
        <label for="confirmPurchase">购买前确认:</label>
        <select id="confirmPurchase">
          <option value="true">是</option>
          <option value="false">否</option>
        </select>
      </div>
    </div>
    
    <button id="saveOptions">保存设置</button>
    <div id="status" class="status" style="display: none;"></div>
  </div>
  
  <script src="js/options.js"></script>
</body>
</html> 