// 监听来自弹出页面的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'getToken') {
    getToken(request.url)
      .then(token => {
        if (token) {
          // 保存token到本地存储
          chrome.storage.local.set({ token }, function() {
            sendResponse({ success: true, token });
          });
        } else {
          sendResponse({ success: false, error: '获取token失败' });
        }
      })
      .catch(error => {
        console.error('获取token出错:', error);
        sendResponse({ success: false, error: error.message });
      });
    
    return true; // 异步响应
  }
  
  else if (request.action === 'collectProduct') {
    collectProduct(request.url, request.token)
      .then(product => {
        if (product) {
          // 保存采集到的商品信息
          chrome.storage.local.set({ collectedProduct: product }, function() {
            sendResponse({ success: true, product });
          });
        } else {
          sendResponse({ success: false, error: '采集商品失败' });
        }
      })
      .catch(error => {
        console.error('采集商品出错:', error);
        sendResponse({ success: false, error: error.message });
      });
    
    return true; // 异步响应
  }
  
  else if (request.action === 'buyProduct') {
    buyProduct(request.product, request.token)
      .then(result => {
        if (result.success) {
          sendResponse({ success: true, orderInfo: result.orderInfo });
        } else {
          sendResponse({ success: false, error: result.error });
        }
      })
      .catch(error => {
        console.error('购买商品出错:', error);
        sendResponse({ success: false, error: error.message });
      });
    
    return true; // 异步响应
  }
  
  else if (request.action === 'open1688CollectionPage') {
    try {
      // 打开1688采集页面
      chrome.tabs.create({ 
        url: 'https://www.1688.com/', 
        active: true 
      }, function(tab) {
        // 将token传递给新打开的页面
        chrome.scripting.executeScript({
          target: { tabId: tab.id },
          function: function(token) {
            // 将token保存到localStorage，以便页面加载后使用
            localStorage.setItem('mall_extension_token', token);
          },
          args: [request.token]
        });
        
        sendResponse({ success: true, tabId: tab.id });
      });
    } catch (error) {
      console.error('打开1688采集页面出错:', error);
      sendResponse({ success: false, error: error.message });
    }
    
    return true; // 异步响应
  }
  
  else if (request.action === 'openPddCollectionPage') {
    try {
      // 打开拼多多采集页面
      chrome.tabs.create({ 
        url: 'https://mobile.yangkeduo.com/', 
        active: true 
      }, function(tab) {
        // 将token传递给新打开的页面
        chrome.scripting.executeScript({
          target: { tabId: tab.id },
          function: function(token) {
            // 将token保存到localStorage，以便页面加载后使用
            localStorage.setItem('mall_extension_token', token);
          },
          args: [request.token]
        });
        
        sendResponse({ success: true, tabId: tab.id });
      });
    } catch (error) {
      console.error('打开拼多多采集页面出错:', error);
      sendResponse({ success: false, error: error.message });
    }
    
    return true; // 异步响应
  }
});

// 从指定网址获取token
async function getToken(url) {
  try {
    // 创建一个新标签页访问指定网址
    const tab = await chrome.tabs.create({ url, active: false });
    
    // 注入内容脚本到新标签页
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: extractToken
    });
    
    // 等待内容脚本提取token
    return new Promise((resolve, reject) => {
      // 监听来自内容脚本的消息
      const listener = function(message, sender) {
        if (sender.tab && sender.tab.id === tab.id && message.action === 'tokenExtracted') {
          chrome.runtime.onMessage.removeListener(listener);
          
          // 关闭标签页
          chrome.tabs.remove(tab.id);
          
          if (message.token) {
            resolve(message.token);
          } else {
            reject(new Error(message.error || '无法获取token'));
          }
        }
      };
      
      chrome.runtime.onMessage.addListener(listener);
      
      // 设置超时
      setTimeout(() => {
        chrome.runtime.onMessage.removeListener(listener);
        chrome.tabs.remove(tab.id);
        reject(new Error('获取token超时'));
      }, 30000); // 30秒超时
    });
  } catch (error) {
    console.error('获取token出错:', error);
    throw error;
  }
}

// 在页面中提取token的函数
function extractToken() {
  try {
    // 这里需要根据实际网站结构来提取token
    // 例如，可能是从localStorage、cookie或页面元素中提取
    
    // 示例：从localStorage中提取token
    const token = localStorage.getItem('token');
    
    if (token) {
      chrome.runtime.sendMessage({ 
        action: 'tokenExtracted', 
        token 
      });
    } else {
      // 示例：从cookie中提取token
      const cookies = document.cookie.split(';');
      let tokenFromCookie = null;
      
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'token' || name === 'auth_token') {
          tokenFromCookie = value;
          break;
        }
      }
      
      if (tokenFromCookie) {
        chrome.runtime.sendMessage({ 
          action: 'tokenExtracted', 
          token: tokenFromCookie 
        });
      } else {
        // 示例：从页面元素中提取token
        // 这需要根据实际网站结构调整
        const tokenElement = document.querySelector('[data-token]');
        
        if (tokenElement && tokenElement.dataset.token) {
          chrome.runtime.sendMessage({ 
            action: 'tokenExtracted', 
            token: tokenElement.dataset.token 
          });
        } else {
          chrome.runtime.sendMessage({ 
            action: 'tokenExtracted', 
            error: '在页面中未找到token' 
          });
        }
      }
    }
  } catch (error) {
    chrome.runtime.sendMessage({ 
      action: 'tokenExtracted', 
      error: error.message 
    });
  }
}

// 采集1688商品信息
async function collectProduct(url, token) {
  try {
    // 创建一个新标签页访问1688商品页面
    const tab = await chrome.tabs.create({ url, active: false });
    
    // 注入内容脚本到新标签页
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: extractProductInfo,
      args: [token]
    });
    
    // 等待内容脚本提取商品信息
    return new Promise((resolve, reject) => {
      // 监听来自内容脚本的消息
      const listener = function(message, sender) {
        if (sender.tab && sender.tab.id === tab.id && message.action === 'productExtracted') {
          chrome.runtime.onMessage.removeListener(listener);
          
          // 关闭标签页
          chrome.tabs.remove(tab.id);
          
          if (message.product) {
            resolve(message.product);
          } else {
            reject(new Error(message.error || '无法获取商品信息'));
          }
        }
      };
      
      chrome.runtime.onMessage.addListener(listener);
      
      // 设置超时
      setTimeout(() => {
        chrome.runtime.onMessage.removeListener(listener);
        chrome.tabs.remove(tab.id);
        reject(new Error('获取商品信息超时'));
      }, 30000); // 30秒超时
    });
  } catch (error) {
    console.error('采集商品出错:', error);
    throw error;
  }
}

// 在1688页面中提取商品信息的函数
function extractProductInfo(token) {
  try {
    // 等待页面加载完成
    if (document.readyState !== 'complete') {
      window.addEventListener('load', () => extractProductInfo(token));
      return;
    }
    
    // 延迟执行，确保页面元素已加载
    setTimeout(() => {
      try {
        // 提取商品信息
        const product = {
          id: getProductId(),
          title: getProductTitle(),
          price: getProductPrice(),
          images: getProductImages(),
          description: getProductDescription(),
          specs: getProductSpecs(),
          seller: getSellerInfo(),
          url: window.location.href
        };
        
        chrome.runtime.sendMessage({ 
          action: 'productExtracted', 
          product 
        });
      } catch (error) {
        chrome.runtime.sendMessage({ 
          action: 'productExtracted', 
          error: error.message 
        });
      }
    }, 2000);
    
    // 获取商品ID
    function getProductId() {
      // 从URL中提取商品ID
      const match = window.location.href.match(/\/(\d+)\.html/);
      return match ? match[1] : null;
    }
    
    // 获取商品标题
    function getProductTitle() {
      const titleElement = document.querySelector('.title-text') || 
                          document.querySelector('.title') || 
                          document.querySelector('h1');
      return titleElement ? titleElement.textContent.trim() : '未知标题';
    }
    
    // 获取商品价格
    function getProductPrice() {
      const priceElement = document.querySelector('.price-text') || 
                          document.querySelector('.price') ||
                          document.querySelector('[class*="price"]');
      return priceElement ? priceElement.textContent.trim() : '未知价格';
    }
    
    // 获取商品图片
    function getProductImages() {
      const images = [];
      const imageElements = document.querySelectorAll('.detail-gallery-img img, .detail-gallery img, [class*="gallery"] img');
      
      imageElements.forEach(img => {
        if (img.src) {
          images.push(img.src);
        }
      });
      
      return images;
    }
    
    // 获取商品描述
    function getProductDescription() {
      const descElement = document.querySelector('.detail-desc') || 
                         document.querySelector('.desc') ||
                         document.querySelector('[class*="description"]');
      return descElement ? descElement.textContent.trim() : '暂无描述';
    }
    
    // 获取商品规格
    function getProductSpecs() {
      const specs = {};
      const specElements = document.querySelectorAll('.detail-attr-item, .attr-item, [class*="spec"]');
      
      specElements.forEach(spec => {
        const key = spec.querySelector('.attr-name, .name')?.textContent.trim();
        const value = spec.querySelector('.attr-value, .value')?.textContent.trim();
        
        if (key && value) {
          specs[key] = value;
        }
      });
      
      return specs;
    }
    
    // 获取卖家信息
    function getSellerInfo() {
      const sellerElement = document.querySelector('.seller-name') || 
                           document.querySelector('.company-name') ||
                           document.querySelector('[class*="seller"]');
      
      return sellerElement ? sellerElement.textContent.trim() : '未知卖家';
    }
  } catch (error) {
    chrome.runtime.sendMessage({ 
      action: 'productExtracted', 
      error: error.message 
    });
  }
}

// 购买商品
async function buyProduct(product, token) {
  try {
    // 创建一个新标签页访问1688商品页面
    const tab = await chrome.tabs.create({ url: product.url, active: false });
    
    // 注入内容脚本到新标签页
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: performBuyAction,
      args: [product, token]
    });
    
    // 等待内容脚本执行购买操作
    return new Promise((resolve, reject) => {
      // 监听来自内容脚本的消息
      const listener = function(message, sender) {
        if (sender.tab && sender.tab.id === tab.id && message.action === 'buyCompleted') {
          chrome.runtime.onMessage.removeListener(listener);
          
          if (message.success) {
            resolve({ 
              success: true, 
              orderInfo: message.orderInfo 
            });
          } else {
            reject(new Error(message.error || '购买失败'));
          }
        }
      };
      
      chrome.runtime.onMessage.addListener(listener);
      
      // 设置超时
      setTimeout(() => {
        chrome.runtime.onMessage.removeListener(listener);
        reject(new Error('购买操作超时'));
      }, 60000); // 60秒超时
    });
  } catch (error) {
    console.error('购买商品出错:', error);
    throw error;
  }
}

// 在1688页面中执行购买操作的函数
function performBuyAction(product, token) {
  try {
    // 等待页面加载完成
    if (document.readyState !== 'complete') {
      window.addEventListener('load', () => performBuyAction(product, token));
      return;
    }
    
    // 延迟执行，确保页面元素已加载
    setTimeout(async () => {
      try {
        // 添加token到请求头
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
          options.headers = options.headers || {};
          options.headers['Authorization'] = `Bearer ${token}`;
          return originalFetch.call(this, url, options);
        };
        
        // 选择商品规格（如果有）
        await selectProductSpecs();
        
        // 设置购买数量
        await setQuantity(1);
        
        // 点击购买按钮
        await clickBuyButton();
        
        // 提交订单
        // 注意：这里可能需要根据实际情况调整，因为提交订单通常需要跳转到新页面
        
        // 模拟成功购买
        chrome.runtime.sendMessage({ 
          action: 'buyCompleted', 
          success: true,
          orderInfo: {
            orderId: 'ORD' + Date.now(),
            product: product.title,
            price: product.price,
            time: new Date().toISOString()
          }
        });
      } catch (error) {
        chrome.runtime.sendMessage({ 
          action: 'buyCompleted', 
          success: false,
          error: error.message 
        });
      }
    }, 2000);
    
    // 选择商品规格
    async function selectProductSpecs() {
      const specItems = document.querySelectorAll('.sku-item, .sku-property-item, [class*="sku"]');
      
      if (specItems.length === 0) {
        return; // 没有规格可选
      }
      
      // 选择第一个可用的规格
      for (const item of specItems) {
        if (!item.classList.contains('disabled') && !item.classList.contains('selected')) {
          item.click();
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }
    
    // 设置购买数量
    async function setQuantity(num) {
      const quantityInput = document.querySelector('.amount-input, input[name="quantity"], [class*="quantity"]');
      
      if (quantityInput) {
        // 清除原有值
        quantityInput.value = '';
        
        // 设置新值
        quantityInput.value = num;
        
        // 触发change事件
        const event = new Event('change', { bubbles: true });
        quantityInput.dispatchEvent(event);
        
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    // 点击购买按钮
    async function clickBuyButton() {
      const buyButton = document.querySelector('.do-buy, .buy-now-btn, [class*="buy"]');
      
      if (buyButton) {
        buyButton.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        throw new Error('未找到购买按钮');
      }
    }
  } catch (error) {
    chrome.runtime.sendMessage({ 
      action: 'buyCompleted', 
      success: false,
      error: error.message 
    });
  }
} 