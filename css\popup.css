body {
  font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  color: #333;
}

.container {
  width: 400px;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
  font-size: 18px;
  text-align: center;
  margin-bottom: 20px;
  color: #1890ff;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

h2 {
  font-size: 16px;
  margin: 0 0 10px;
  color: #333;
}

.section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #eee;
}

.form-group {
  margin-bottom: 12px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 14px;
}

input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 14px;
}

button {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #40a9ff;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.status {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

#tokenStatusText,
#collectStatusText,
#buyStatusText {
  font-weight: bold;
}

.success {
  color: #52c41a;
}

.error {
  color: #f5222d;
}

.loading {
  color: #1890ff;
} 